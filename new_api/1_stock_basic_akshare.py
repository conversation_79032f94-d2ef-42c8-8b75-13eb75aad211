"""
股票基本信息数据入库程序 - 使用AKShare接口
Created on 2024-12-16
@author: AKShare版本
"""
import pandas as pd
import akshare as ak
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
import os
from dotenv import load_dotenv
from contextlib import contextmanager
from log_config import setup_logger, log_progress, log_error, send_notification

# 初始化环境变量
load_dotenv()

# 配置日志系统 - 使用脚本文件名而不是__name__
script_name = os.path.splitext(os.path.basename(__file__))[0]
logger = setup_logger(script_name)

# 数据库连接配置
DB_CONFIG = {
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', '31490600'),
    'host': os.getenv('DB_HOST', '**********'),
    'port': os.getenv('DB_PORT', '3306'),
    'database': os.getenv('DB_NAME', 'instockdb'),
    'charset': 'utf8mb4'
}

# 新表结构定义 - 只保留股票代码和股票名称
TABLE_SCHEMA = """
CREATE TABLE IF NOT EXISTS `stock_basic_ask` (
    `stock_code` VARCHAR(10) NOT NULL COMMENT '股票代码',
    `stock_name` VARCHAR(50) NOT NULL COMMENT '股票名称',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`stock_code`),
    KEY `idx_stock_name` (`stock_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='股票基本信息表(AKShare版本)';
"""

# 全局数据库引擎
_DB_ENGINE = None

def get_db_engine():
    """获取数据库引擎（单例模式）"""
    global _DB_ENGINE
    if _DB_ENGINE is None:
        _DB_ENGINE = create_engine(
            f"mysql+pymysql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@"
            f"{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}?charset={DB_CONFIG['charset']}",
            pool_size=10,
            max_overflow=20,
            pool_recycle=3600,
            pool_pre_ping=True,
            echo=False  # 生产环境关闭SQL日志
        )
    return _DB_ENGINE

@contextmanager
def db_connection():
    """数据库连接上下文管理"""
    engine = get_db_engine()
    conn = None
    try:
        conn = engine.connect()
        yield conn
    except SQLAlchemyError as e:
        log_error(logger, f"数据库连接失败: {str(e)}")
        raise
    finally:
        if conn:
            conn.close()

def init_database():
    """初始化数据库表结构"""
    try:
        with db_connection() as conn:
            conn.execute(text(TABLE_SCHEMA))
            logger.info("数据库表 stock_basic_ask 初始化完成")
    except Exception as e:
        log_error(logger, f"数据库初始化失败: {str(e)}")
        raise

def validate_stock_data(df):
    """数据校验与清洗"""
    if df.empty:
        log_error(logger, "接收到空的股票数据")
        return df

    # 创建数据框的副本
    df = df.copy()

    # 只保留需要的列：代码和名称
    if '代码' in df.columns and '名称' in df.columns:
        df = df[['代码', '名称']].copy()
        # 重命名列
        df = df.rename(columns={'代码': 'stock_code', '名称': 'stock_name'})
    else:
        log_error(logger, "数据中缺少必要的列：代码或名称")
        return pd.DataFrame()

    try:
        # 清理股票名称中的特殊字符
        df['stock_name'] = df['stock_name'].str.replace(r'[*]', '', regex=True)
        
        # 删除重复项
        df = df.drop_duplicates(subset=['stock_code'], keep='first')
        
        # 过滤无效数据
        valid_df = df.dropna(subset=['stock_code', 'stock_name'])
        
        # 过滤掉代码为空或长度不合理的记录
        valid_df = valid_df[valid_df['stock_code'].str.len() == 6]
        
        if len(valid_df) < len(df):
            log_progress(logger, f"数据清洗：从 {len(df)} 条减少到 {len(valid_df)} 条有效记录")

        return valid_df

    except Exception as e:
        log_error(logger, f"数据验证失败: {str(e)}")
        raise

def get_stock_data():
    """获取股票数据(带重试机制)"""
    import time

    max_retries = 3
    retry_delay = 2  # 重试间隔秒数

    for attempt in range(max_retries):
        try:
            log_progress(logger, f"开始获取股票基本信息数据 (尝试 {attempt + 1}/{max_retries})")

            # 使用AKShare获取沪深京A股实时行情数据
            stock_zh_a_spot_em_df = ak.stock_zh_a_spot_em()

            if stock_zh_a_spot_em_df.empty:
                raise ValueError("AKShare API返回空数据")

            log_progress(logger, f"成功获取 {len(stock_zh_a_spot_em_df)} 条原始股票数据")
            
            # 显示数据结构信息
            log_progress(logger, f"数据列名: {list(stock_zh_a_spot_em_df.columns)}")
            
            validated_df = validate_stock_data(stock_zh_a_spot_em_df)

            if validated_df.empty:
                raise ValueError("数据验证后无有效记录")

            log_progress(logger, f"数据验证完成，有效记录 {len(validated_df)} 条")
            return validated_df

        except Exception as e:
            error_msg = f"第 {attempt + 1} 次尝试失败: {str(e)}"

            if attempt < max_retries - 1:
                log_progress(logger, f"{error_msg}，{retry_delay}秒后重试...")
                time.sleep(retry_delay)
                retry_delay *= 2  # 指数退避
                continue
            else:
                log_error(logger, f"所有重试均失败: {error_msg}")
                raise RuntimeError(f"数据获取失败: {str(e)}")

def save_stock_data(df):
    """安全保存数据到数据库"""
    import time

    if df.empty:
        log_progress(logger, "无有效数据需要保存")
        return

    start_time = time.time()
    log_progress(logger, f"开始保存 {len(df)} 条股票数据到数据库")

    try:
        with db_connection() as conn:
            # 使用事务处理
            with conn.begin():
                # 清空历史数据
                truncate_start = time.time()
                conn.execute(text("TRUNCATE TABLE stock_basic_ask"))
                truncate_time = time.time() - truncate_start
                log_progress(logger, f"清空历史数据完成，耗时 {truncate_time:.2f} 秒")

                # 批量插入数据
                insert_start = time.time()
                df.to_sql(
                    name='stock_basic_ask',
                    con=conn,
                    if_exists='append',
                    index=False,
                    chunksize=500,  # 优化批次大小
                    method='multi'
                )
                insert_time = time.time() - insert_start

            total_time = time.time() - start_time
            log_progress(logger, f"数据保存完成: {len(df)} 条记录")
            log_progress(logger, f"性能统计: 插入耗时 {insert_time:.2f}秒, 总耗时 {total_time:.2f}秒")
            log_progress(logger, f"写入速度: {len(df)/total_time:.0f} 条/秒")

    except SQLAlchemyError as e:
        log_error(logger, f"数据写入失败: {str(e)}")
        raise
    except Exception as e:
        log_error(logger, f"数据保存过程中发生异常: {str(e)}")
        raise

def main():
    """主函数"""
    import time

    start_time = time.time()
    log_progress(logger, "=== 股票基本信息更新开始 (AKShare版本) ===")

    try:
        # 初始化数据库
        init_start = time.time()
        init_database()
        init_time = time.time() - init_start
        log_progress(logger, f"数据库初始化完成，耗时 {init_time:.2f} 秒")

        # 获取股票数据
        fetch_start = time.time()
        stock_df = get_stock_data()
        fetch_time = time.time() - fetch_start
        log_progress(logger, f"数据获取完成，耗时 {fetch_time:.2f} 秒")

        # 保存数据
        save_stock_data(stock_df)

        # 总结
        total_time = time.time() - start_time
        log_progress(logger, f"=== 股票基本信息更新完成 ===")
        log_progress(logger, f"总计处理 {len(stock_df)} 条记录，总耗时 {total_time:.2f} 秒")
        log_progress(logger, f"平均处理速度: {len(stock_df)/total_time:.0f} 条/秒")

        return True

    except Exception as e:
        total_time = time.time() - start_time
        error_msg = str(e)
        log_error(logger, f"程序执行异常 (耗时 {total_time:.2f} 秒): {error_msg}")

        # 保留失败时的通知
        send_notification(
            message=f"股票基本信息更新失败(AKShare): {error_msg}",
            title="股票基本信息更新异常",
            tags="基本信息|异常|AKShare"
        )
        return False

if __name__ == '__main__':
    import sys
    
    # 测试AKShare接口
    print("=== 测试AKShare接口 ===")
    try:
        stock_zh_a_spot_em_df = ak.stock_zh_a_spot_em()
        print(f"获取到 {len(stock_zh_a_spot_em_df)} 条股票数据")
        print("数据示例:")
        print(stock_zh_a_spot_em_df[['代码', '名称']].head(10))
    except Exception as e:
        print(f"测试失败: {e}")
    
    print("\n=== 开始正式执行 ===")
    success = main()
    if not success:
        sys.exit(1)
