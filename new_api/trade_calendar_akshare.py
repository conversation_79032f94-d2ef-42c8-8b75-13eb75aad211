"""
交易日历模块 - 使用AKShare的tool_trade_date_hist_sina接口
Created on 2024-12-16
"""
import pandas as pd
import akshare as ak
import os
from datetime import datetime, timedelta
import functools
import time
import sys
from log_config import setup_logger, log_progress, log_error, log_warning

# 获取当前脚本的路径和文件名
current_script_path = os.path.dirname(os.path.abspath(__file__))
script_name = os.path.splitext(os.path.basename(__file__))[0]

# 配置日志系统
logger = setup_logger(script_name)

# 缓存装饰器
def cache_result(expire_seconds=3600):
    """缓存函数结果的装饰器,有效期为指定秒数"""
    cache = {}
    
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 创建缓存键
            key = str(args) + str(kwargs)
            
            # 检查缓存是否有效
            if key in cache:
                result, timestamp = cache[key]
                if time.time() - timestamp < expire_seconds:
                    return result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache[key] = (result, time.time())
            return result
        
        return wrapper
    
    return decorator

@cache_result(expire_seconds=86400)  # 缓存一天
def get_trade_calendar_akshare():
    """
    使用AKShare获取交易日历数据
    
    Returns:
    --------
    pandas.DataFrame
        包含交易日历信息的DataFrame
        列包含: trade_date (交易日期)
    """
    try:
        # 使用AKShare获取交易日历
        tool_trade_date_hist_sina_df = ak.tool_trade_date_hist_sina()
        log_progress(logger, f"成功获取交易日历数据: {len(tool_trade_date_hist_sina_df)}条记录")
        
        # 检查数据是否为空
        if tool_trade_date_hist_sina_df.empty:
            log_warning(logger, f"AKShare API返回空数据，可能原因：")
            log_warning(logger, f"1. 网络连接问题")
            log_warning(logger, f"2. API服务暂时不可用")
            
        return tool_trade_date_hist_sina_df

    except Exception as e:
        log_error(logger, f"获取交易日历数据失败: {str(e)}")
        log_error(logger, f"请检查：1. 网络连接是否正常 2. AKShare版本是否最新")
        return pd.DataFrame()  # 返回空DataFrame

def filter_trade_calendar_by_date_range(df, start_date=None, end_date=None):
    """
    根据日期范围过滤交易日历数据
    
    Parameters:
    -----------
    df : pandas.DataFrame
        交易日历数据
    start_date : str, 可选
        开始日期,格式为'YYYY-MM-DD'
    end_date : str, 可选
        结束日期,格式为'YYYY-MM-DD'
        
    Returns:
    --------
    pandas.DataFrame
        过滤后的交易日历数据
    """
    if df.empty:
        return df
        
    # 确保日期列是datetime类型
    if 'trade_date' in df.columns:
        df['trade_date'] = pd.to_datetime(df['trade_date'])
        
        # 应用日期过滤
        if start_date:
            start_date = pd.to_datetime(start_date)
            df = df[df['trade_date'] >= start_date]
            
        if end_date:
            end_date = pd.to_datetime(end_date)
            df = df[df['trade_date'] <= end_date]
    
    return df

def is_trade_day(date=None):
    """
    判断给定日期是否为交易日
    
    Parameters:
    -----------
    date : datetime.date, datetime.datetime 或 str, 可选
        需要判断的日期,可以是datetime对象或'YYYY-MM-DD'格式的字符串
        如果为None,则默认为当天
        
    Returns:
    --------
    bool
        True表示是交易日,False表示非交易日
    """
    try:
        # 处理日期参数
        if date is None:
            date = datetime.now().date()
        elif isinstance(date, str):
            date = datetime.strptime(date, '%Y-%m-%d').date()
        elif isinstance(date, datetime):
            date = date.date()
            
        # 获取交易日历
        calendar_df = get_trade_calendar_akshare()
        
        if calendar_df.empty:
            log_warning(logger, f"未找到交易日历数据")
            return False
            
        # 确保日期列是datetime类型
        calendar_df['trade_date'] = pd.to_datetime(calendar_df['trade_date'])
        
        # 转换查询日期为pandas datetime
        query_date = pd.to_datetime(date)
        
        # 判断是否为交易日
        is_trade = query_date.date() in calendar_df['trade_date'].dt.date.values
        
        return is_trade
        
    except Exception as e:
        log_error(logger, f"判断交易日失败: {str(e)}")
        return False

def get_latest_trade_day(date=None):
    """
    获取给定日期当天或之前的最近一个交易日
    
    Parameters:
    -----------
    date : datetime.date, datetime.datetime 或 str, 可选
        参考日期,可以是datetime对象或'YYYY-MM-DD'格式的字符串
        如果为None,则默认为当天
        
    Returns:
    --------
    datetime.date
        最近的交易日日期
    """
    try:
        # 处理日期参数
        if date is None:
            date = datetime.now().date()
        elif isinstance(date, str):
            date = datetime.strptime(date, '%Y-%m-%d').date()
        elif isinstance(date, datetime):
            date = date.date()
            
        # 如果当天是交易日,直接返回
        if is_trade_day(date):
            return date
            
        # 获取交易日历
        calendar_df = get_trade_calendar_akshare()
        
        if calendar_df.empty:
            log_error(logger, f"未找到交易日历数据")
            return date
            
        # 确保日期列是datetime类型
        calendar_df['trade_date'] = pd.to_datetime(calendar_df['trade_date'])
        
        # 转换查询日期为pandas datetime
        query_date = pd.to_datetime(date)
        
        # 找到小于等于给定日期的最大交易日
        trade_days = calendar_df[calendar_df['trade_date'] <= query_date]['trade_date']
        
        if trade_days.empty:
            log_error(logger, f"未找到{date}之前的交易日")
            return date
            
        latest_date = trade_days.max()
        return latest_date.date()
        
    except Exception as e:
        log_error(logger, f"获取最近交易日失败: {str(e)}")
        return date

def get_next_trade_day(date=None):
    """
    获取给定日期之后的下一个交易日
    
    Parameters:
    -----------
    date : datetime.date, datetime.datetime 或 str, 可选
        参考日期,可以是datetime对象或'YYYY-MM-DD'格式的字符串
        如果为None,则默认为当天
        
    Returns:
    --------
    datetime.date
        下一个交易日日期
    """
    try:
        # 处理日期参数
        if date is None:
            date = datetime.now().date()
        elif isinstance(date, str):
            date = datetime.strptime(date, '%Y-%m-%d').date()
        elif isinstance(date, datetime):
            date = date.date()
            
        # 获取交易日历
        calendar_df = get_trade_calendar_akshare()
        
        if calendar_df.empty:
            log_error(logger, f"未找到交易日历数据")
            return date + timedelta(days=1)
            
        # 确保日期列是datetime类型
        calendar_df['trade_date'] = pd.to_datetime(calendar_df['trade_date'])
        
        # 转换查询日期为pandas datetime
        query_date = pd.to_datetime(date)
        
        # 找到大于给定日期的最小交易日
        trade_days = calendar_df[calendar_df['trade_date'] > query_date]['trade_date']
        
        if trade_days.empty:
            log_error(logger, f"未找到{date}之后的交易日")
            return date + timedelta(days=1)
            
        next_date = trade_days.min()
        return next_date.date()
        
    except Exception as e:
        log_error(logger, f"获取下一交易日失败: {str(e)}")
        return date + timedelta(days=1)

def get_friday_of_current_week(date):
    """
    获取指定日期所在周的周五日期
    如果当前日期是周六或周日，返回刚刚过去的周五
    
    参数:
    -----------
    date : datetime.date
        要查找对应周五的日期
        
    返回:
    --------
    datetime.date
        同一周的周五日期（如果是周六日则是刚过去的周五）
    """
    # 计算到周五的天数 (weekday 4 = 周五)
    weekday = date.weekday()
    if weekday < 5:  # 周一到周五
        days_to_friday = 4 - weekday
    else:  # 周六或周日，返回刚刚过去的周五
        days_to_friday = -(weekday - 4)
    
    friday = date + timedelta(days=days_to_friday)
    return friday

if __name__ == "__main__":
    # 测试AKShare交易日历功能
    print("=== AKShare 交易日历测试 ===")
    
    # 获取交易日历数据
    tool_trade_date_hist_sina_df = ak.tool_trade_date_hist_sina()
    print(f"获取到 {len(tool_trade_date_hist_sina_df)} 条交易日历记录")
    print(tool_trade_date_hist_sina_df.head())
    print(tool_trade_date_hist_sina_df.tail())
    
    today = datetime.now().date()
    
    # 测试是否为交易日
    is_today_trade = is_trade_day(today)
    log_progress(logger, f"今天 {today} {'是' if is_today_trade else '不是'}交易日")
    
    # 获取最近的交易日
    latest_trade_day = get_latest_trade_day(today)
    log_progress(logger, f"最近交易日是: {latest_trade_day}")
    
    # 获取下一个交易日
    next_trade_day = get_next_trade_day(today)
    log_progress(logger, f"下一个交易日是: {next_trade_day}")
    
    # 显示最近几个交易日
    recent_trade_days = tool_trade_date_hist_sina_df.tail(10)
    log_progress(logger, f"最近10个交易日:")
    for _, row in recent_trade_days.iterrows():
        print(f"  {row['trade_date']}")
