"""
股票日线数据入库程序 - 使用AKShare接口
Created on 2024-12-16
@author: AKShare版本
"""
import pandas as pd
import akshare as ak
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
import os
import time
from datetime import datetime, timedelta
from dotenv import load_dotenv
from contextlib import contextmanager
from sqlalchemy.pool import QueuePool
import warnings
import pathlib
from log_config import setup_logger, log_progress, log_error, log_warning, send_notification

# 屏蔽警告信息
warnings.filterwarnings('ignore', category=FutureWarning)
warnings.filterwarnings('ignore', category=UserWarning)

# 初始化环境变量
load_dotenv()

# 创建日志目录
log_dir = pathlib.Path(__file__).parent / 'log'
log_dir.mkdir(exist_ok=True)

# 获取脚本名称
script_name = os.path.splitext(os.path.basename(__file__))[0]

# 配置日志系统
logger = setup_logger(script_name)

# 数据库连接配置
DB_CONFIG = {
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', '31490600'),
    'host': os.getenv('DB_HOST', '**********'),
    'port': os.getenv('DB_PORT', '3306'),
    'database': os.getenv('DB_NAME', 'instockdb'),
    'charset': 'utf8mb4'
}

# 数据库连接池配置
DB_POOL_SIZE = 10
DB_MAX_OVERFLOW = 20
DB_POOL_TIMEOUT = 30

# 新表结构定义 - 使用AKShare数据
TABLE_SCHEMA = """
CREATE TABLE IF NOT EXISTS `stock_daily_ask` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `stock_code` VARCHAR(6) NOT NULL COMMENT '股票代码',
    `name` VARCHAR(50) NOT NULL COMMENT '股票名称',
    `trade_date` DATE NOT NULL COMMENT '交易日期',
    `open` DECIMAL(10,4) COMMENT '开盘价(前复权)',
    `high` DECIMAL(10,4) COMMENT '最高价(前复权)',
    `low` DECIMAL(10,4) COMMENT '最低价(前复权)',
    `close` DECIMAL(10,4) COMMENT '收盘价(前复权)',
    `pre_close` DECIMAL(10,4) COMMENT '昨收价',
    `change` DECIMAL(10,4) COMMENT '涨跌额',
    `pct_chg` DECIMAL(10,4) COMMENT '涨跌幅',
    `vol` DECIMAL(20,4) COMMENT '成交量(手)',
    `amount` DECIMAL(20,4) COMMENT '成交额(千元)',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `uk_stock_code_trade_date` (`stock_code`, `trade_date`),
    KEY `idx_stock_code` (`stock_code`),
    KEY `idx_trade_date` (`trade_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='股票日线行情表(AKShare前复权)';
"""

# 全局数据库引擎
_DB_ENGINE = None

def get_db_engine():
    """获取数据库引擎（单例模式）"""
    global _DB_ENGINE
    if _DB_ENGINE is None:
        _DB_ENGINE = create_engine(
            f"mysql+pymysql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@"
            f"{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}?charset={DB_CONFIG['charset']}",
            poolclass=QueuePool,
            pool_size=DB_POOL_SIZE,
            max_overflow=DB_MAX_OVERFLOW,
            pool_timeout=DB_POOL_TIMEOUT,
            pool_pre_ping=True,
            pool_recycle=3600,  # 连接回收时间
            echo=False  # 生产环境关闭SQL日志
        )
    return _DB_ENGINE

@contextmanager
def db_connection():
    """数据库连接上下文管理"""
    engine = get_db_engine()
    conn = None
    try:
        conn = engine.connect()
        yield conn
    except SQLAlchemyError as e:
        log_error(logger, f"数据库连接失败: {str(e)}")
        raise
    finally:
        if conn:
            conn.close()

def init_database():
    """初始化数据库表结构"""
    try:
        with db_connection() as conn:
            conn.execute(text(TABLE_SCHEMA))
            log_progress(logger, "数据库表 stock_daily_ask 初始化完成")
    except Exception as e:
        log_error(logger, f"数据库初始化失败: {str(e)}")
        raise

def get_stock_list():
    """获取股票列表"""
    try:
        log_progress(logger, "开始获取股票列表...")
        
        # 从stock_basic_ask表获取股票列表
        with db_connection() as conn:
            query = "SELECT stock_code, stock_name FROM stock_basic_ask ORDER BY stock_code"
            stock_list = pd.read_sql(query, conn)
            
        if stock_list.empty:
            log_warning(logger, "stock_basic_ask表为空，尝试使用AKShare获取股票列表")
            # 如果表为空，使用AKShare获取
            stock_zh_a_spot_em_df = ak.stock_zh_a_spot_em()
            stock_list = stock_zh_a_spot_em_df[['代码', '名称']].copy()
            stock_list.columns = ['stock_code', 'stock_name']
            
        log_progress(logger, f"成功获取 {len(stock_list)} 只股票")
        return stock_list
        
    except Exception as e:
        log_error(logger, f"获取股票列表失败: {str(e)}")
        raise

def get_stock_hist_data(symbol, start_date, end_date, max_retries=3):
    """获取单只股票的历史数据"""
    for attempt in range(max_retries):
        try:
            # 使用AKShare获取前复权历史数据
            stock_zh_a_hist_df = ak.stock_zh_a_hist(
                symbol=symbol, 
                period="daily", 
                start_date=start_date, 
                end_date=end_date, 
                adjust="qfq"  # 前复权
            )
            
            if stock_zh_a_hist_df.empty:
                log_warning(logger, f"股票 {symbol} 无历史数据")
                return pd.DataFrame()
                
            return stock_zh_a_hist_df
            
        except Exception as e:
            if attempt < max_retries - 1:
                wait_time = (attempt + 1) * 2
                log_warning(logger, f"获取股票 {symbol} 数据失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}，{wait_time}秒后重试")
                time.sleep(wait_time)
            else:
                log_error(logger, f"获取股票 {symbol} 数据最终失败: {str(e)}")
                return pd.DataFrame()

def validate_daily_data(df, stock_code, stock_name):
    """数据校验与清洗"""
    if df.empty:
        return df

    # 创建数据框的副本
    df = df.copy()

    try:
        # 添加股票代码和名称
        df['stock_code'] = stock_code
        df['name'] = stock_name
        
        # 重命名列以匹配数据库字段
        column_mapping = {
            '日期': 'trade_date',
            '开盘': 'open',
            '收盘': 'close',
            '最高': 'high',
            '最低': 'low',
            '成交量': 'vol',
            '成交额': 'amount',
            '振幅': 'amplitude',
            '涨跌幅': 'pct_chg',
            '涨跌额': 'change',
            '换手率': 'turnover'
        }
        
        df = df.rename(columns=column_mapping)
        
        # 转换日期格式
        df['trade_date'] = pd.to_datetime(df['trade_date']).dt.date
        
        # 计算昨收价 (收盘价 - 涨跌额)
        df['pre_close'] = df['close'] - df['change']
        
        # 成交量转换为手 (AKShare返回的是股数，需要除以100)
        df['vol'] = df['vol'] / 100
        
        # 成交额转换为千元 (AKShare返回的是元，需要除以1000)
        df['amount'] = df['amount'] / 1000
        
        # 只保留需要的列
        columns_to_keep = ['stock_code', 'name', 'trade_date', 'open', 'high', 'low', 
                          'close', 'pre_close', 'change', 'pct_chg', 'vol', 'amount']
        
        df = df[columns_to_keep]
        
        # 删除重复项
        df = df.drop_duplicates(subset=['stock_code', 'trade_date'], keep='first')
        
        # 过滤无效数据
        valid_df = df.dropna(subset=['stock_code', 'name', 'trade_date'])
        
        return valid_df

    except Exception as e:
        log_error(logger, f"数据验证失败: {str(e)}")
        return pd.DataFrame()

def truncate_table():
    """清空数据表"""
    try:
        with db_connection() as conn:
            conn.execute(text("TRUNCATE TABLE stock_daily_ask"))
            log_progress(logger, "数据表 stock_daily_ask 已清空")
    except Exception as e:
        log_error(logger, f"清空数据表失败: {str(e)}")
        raise

def save_daily_data(df):
    """保存数据到数据库"""
    if df.empty:
        log_progress(logger, "无数据需要保存")
        return

    start_time = time.time()
    max_retries = 3
    batch_size = 3000  # 优化批次大小
    total_records = len(df)
    processed_records = 0

    log_progress(logger, f"开始保存数据到数据库，总记录数: {total_records:,}，批次大小: {batch_size:,}")

    # 按批次保存数据
    total_batches = (total_records + batch_size - 1) // batch_size

    for i in range(0, total_records, batch_size):
        batch_start_time = time.time()
        batch_df = df.iloc[i:i+batch_size]
        batch_num = i // batch_size + 1
        retry_count = 0

        while retry_count < max_retries:
            try:
                with db_connection() as conn:
                    batch_df.to_sql(
                        'stock_daily_ask',
                        conn,
                        if_exists='append',
                        index=False,
                        method='multi',
                        chunksize=1000  # 内部分块大小
                    )

                    processed_records += len(batch_df)
                    batch_time = time.time() - batch_start_time

                    # 进度报告
                    if batch_num % 5 == 0 or processed_records == total_records:
                        progress_pct = processed_records / total_records * 100
                        elapsed_time = time.time() - start_time
                        avg_speed = processed_records / elapsed_time if elapsed_time > 0 else 0

                        log_progress(logger,
                            f"批次 {batch_num}/{total_batches}: {processed_records:,}/{total_records:,} "
                            f"({progress_pct:.1f}%) - 批次耗时: {batch_time:.2f}s, 平均速度: {avg_speed:.0f}条/秒")

                    break  # 保存成功，跳出重试循环

            except SQLAlchemyError as e:
                retry_count += 1
                if retry_count < max_retries:
                    log_progress(logger, f"批次 {batch_num} 写入失败，第 {retry_count} 次重试: {str(e)}")
                    time.sleep(retry_count)  # 递增等待时间
                else:
                    log_error(logger, f"批次 {batch_num} 写入失败，已重试 {max_retries} 次: {str(e)}")
                    raise

    total_time = time.time() - start_time
    avg_speed = processed_records / total_time if total_time > 0 else 0
    log_progress(logger, f"数据保存完成: {processed_records:,} 条记录，总耗时: {total_time:.2f}秒，平均速度: {avg_speed:.0f}条/秒")

def get_and_save_daily_data():
    """获取和保存股票日线数据"""
    try:
        # 清空数据表
        truncate_table()

        # 获取股票列表
        stock_list = get_stock_list()

        # 计算日期范围(近2年)
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=730)).strftime('%Y%m%d')

        log_progress(logger, f"开始获取日线数据，股票数量: {len(stock_list)}，日期范围: {start_date} - {end_date}")

        all_data = []
        failed_stocks = []

        # 逐只股票获取数据
        for idx, row in stock_list.iterrows():
            stock_code = row['stock_code']
            stock_name = row['stock_name']

            try:
                # 获取历史数据
                hist_data = get_stock_hist_data(stock_code, start_date, end_date)

                if not hist_data.empty:
                    # 数据清洗
                    clean_data = validate_daily_data(hist_data, stock_code, stock_name)
                    if not clean_data.empty:
                        all_data.append(clean_data)

                # 进度报告
                if (idx + 1) % 50 == 0:
                    progress_pct = (idx + 1) / len(stock_list) * 100
                    log_progress(logger, f"进度: {idx + 1}/{len(stock_list)} ({progress_pct:.1f}%) - 当前股票: {stock_code}")

                # 控制请求频率，避免被限制
                time.sleep(0.1)

            except Exception as e:
                log_error(logger, f"处理股票 {stock_code} 失败: {str(e)}")
                failed_stocks.append(stock_code)
                continue

        # 合并所有数据
        if all_data:
            final_df = pd.concat(all_data, ignore_index=True)
            log_progress(logger, f"数据获取完成，共 {len(final_df)} 条记录")

            # 保存数据
            save_daily_data(final_df)

            if failed_stocks:
                log_warning(logger, f"失败股票数量: {len(failed_stocks)}, 失败股票: {failed_stocks[:10]}...")
        else:
            log_error(logger, "未获取到任何有效数据")

    except Exception as e:
        log_error(logger, f"数据获取过程发生错误: {str(e)}")
        raise

def main():
    """主函数"""
    start_time = time.time()
    log_progress(logger, "=== 股票日线数据同步开始 (AKShare版本) ===")

    try:
        # 初始化数据库
        init_start = time.time()
        init_database()
        init_time = time.time() - init_start
        log_progress(logger, f"数据库初始化完成，耗时 {init_time:.2f} 秒")

        # 获取和保存日线数据
        data_start = time.time()
        get_and_save_daily_data()
        data_time = time.time() - data_start

        # 总结
        total_time = time.time() - start_time
        log_progress(logger, f"=== 股票日线数据同步完成 ===")
        log_progress(logger, f"数据处理耗时: {data_time:.2f} 秒，总耗时: {total_time:.2f} 秒")

        return True

    except Exception as e:
        total_time = time.time() - start_time
        error_msg = str(e)
        log_error(logger, f"程序执行异常 (耗时 {total_time:.2f} 秒): {error_msg}")

        send_notification(
            message=f"日线数据同步失败(AKShare): {error_msg}",
            title="日线数据同步失败",
            tags="日线数据|失败|AKShare"
        )
        return False

if __name__ == '__main__':
    import sys

    # 测试AKShare接口
    print("=== 测试AKShare接口 ===")
    try:
        test_symbol = "000001"
        test_start = "20240101"
        test_end = "20240131"

        stock_zh_a_hist_df = ak.stock_zh_a_hist(
            symbol=test_symbol,
            period="daily",
            start_date=test_start,
            end_date=test_end,
            adjust="qfq"
        )
        print(f"测试股票 {test_symbol} 获取到 {len(stock_zh_a_hist_df)} 条数据")
        print("数据示例:")
        print(stock_zh_a_hist_df.head())
    except Exception as e:
        print(f"测试失败: {e}")

    print("\n=== 开始正式执行 ===")
    success = main()
    if not success:
        sys.exit(1)
