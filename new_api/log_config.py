"""
集中式日志配置模块 - 提供统一的日志记录功能
Created on 2024-05-30
"""
import logging
import os
import pathlib
from datetime import datetime
from typing import Optional, Dict, Any, Union
from serverchan_sdk import sc_send

# ServerChan配置
SERVERCHAN_KEY = "sctp5099tkrhuzdrsrepnwn352fyftq"

# 日志格式化配置
DEFAULT_LOG_FORMAT = '%(asctime)s - %(levelname)s - %(name)s - %(message)s'
DEFAULT_DATE_FORMAT = '%Y-%m-%d %H:%M:%S'
DEFAULT_LOG_LEVEL = logging.INFO

# 确保日志目录存在
def ensure_log_dir() -> str:
    """确保日志目录存在并返回路径"""
    log_dir = pathlib.Path(__file__).parent / 'log'
    log_dir.mkdir(exist_ok=True)
    return str(log_dir)

def setup_logger(
    module_name: str,
    log_level: int = DEFAULT_LOG_LEVEL,
    log_format: str = DEFAULT_LOG_FORMAT,
    date_format: str = DEFAULT_DATE_FORMAT,
    include_date_in_filename: bool = False,
    clear_existing_handlers: bool = True,
    console_output: bool = True
) -> logging.Logger:
    """
    设置并返回一个配置好的logger实例
    
    Args:
        module_name: 模块名称, 通常使用 __name__
        log_level: 日志级别
        log_format: 日志格式
        date_format: 日期时间格式
        include_date_in_filename: 是否在日志文件名中包含日期
        clear_existing_handlers: 是否清除logger现有的处理器
        console_output: 是否输出到控制台
        
    Returns:
        配置好的logger实例
    """
    logger = logging.getLogger(module_name)
    logger.setLevel(log_level)
    
    # 清除可能存在的处理器
    if clear_existing_handlers and logger.handlers:
        logger.handlers.clear()
    
    # 获取日志目录
    log_dir = ensure_log_dir()
    
    # 构建日志文件名
    base_name = module_name.split('.')[-1]  # 使用模块名的最后部分
    if include_date_in_filename:
        log_file_name = f"{base_name}_{datetime.now().strftime('%Y%m%d')}.log"
    else:
        log_file_name = f"{base_name}.log"
    
    log_file_path = os.path.join(log_dir, log_file_name)
    
    # 创建文件处理器
    file_handler = logging.FileHandler(log_file_path, encoding='utf-8')
    file_formatter = logging.Formatter(log_format, date_format)
    file_handler.setFormatter(file_formatter)
    logger.addHandler(file_handler)
    
    # 创建控制台处理器
    if console_output:
        console_handler = logging.StreamHandler()
        console_formatter = logging.Formatter(log_format, date_format)
        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)
    
    return logger

def log_progress(logger: logging.Logger, message: str) -> None:
    """记录进度信息"""
    logger.info(f"[进度] {message}")

def log_error(logger: logging.Logger, message: str) -> None:
    """记录错误信息"""
    logger.error(f"[错误] {message}")

def log_warning(logger: logging.Logger, message: str) -> None:
    """记录警告信息"""
    logger.warning(f"[警告] {message}")

def send_notification(message: str, title: Optional[str] = None, tags: Optional[str] = None) -> None:
    """
    发送ServerChan通知
    
    Args:
        message: 通知消息内容
        title: 通知标题
        tags: 消息标签
    """
    logger = logging.getLogger('notification')
    
    try:
        if SERVERCHAN_KEY:
            if title is None:
                title = "股票数据处理通知"
            
            # 确保消息是字符串类型
            if not isinstance(message, str):
                message = str(message)
            
            # 移除可能导致问题的\r字符
            cleaned_message = message.replace("\r", "")
            
            # 发送消息
            sc_send(SERVERCHAN_KEY, title, cleaned_message, {"tags": tags} if tags else None)
            logger.info(f"通知已发送: {title}")
        else:
            logger.warning("未配置ServerChan密钥,无法发送通知")
    except Exception as e:
        logger.error(f"发送通知失败: {str(e)}")

# 方便直接引用的默认logger
default_logger = setup_logger('root_default') 