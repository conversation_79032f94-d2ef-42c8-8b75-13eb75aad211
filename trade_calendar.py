"""
交易日历模块 - 使用Tushare的trade_cal接口
Created on 2024-05-30
"""
import pandas as pd
import tushare as ts
import os
from datetime import datetime, timedelta
from dotenv import load_dotenv
import functools
import time
import sys
from log_config import setup_logger, log_progress, log_error, log_warning

# 初始化环境变量
load_dotenv()

# 获取当前脚本的路径和文件名
current_script_path = os.path.dirname(os.path.abspath(__file__))
script_name = os.path.splitext(os.path.basename(__file__))[0]

# 配置日志系统
logger = setup_logger(script_name)

# Tushare Token
TUSHARE_TOKEN = os.getenv('TUSHARE_TOKEN', '5aba669a485730beded0d604bd8ccfdaca39f8c9c09ef2d0ed78ed77')

# 初始化Tushare API
def get_pro_api():
    """获取Tushare API对象"""
    return ts.pro_api(TUSHARE_TOKEN)

# 缓存装饰器
def cache_result(expire_seconds=3600):
    """缓存函数结果的装饰器,有效期为指定秒数"""
    cache = {}
    
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 创建缓存键
            key = str(args) + str(kwargs)
            
            # 检查缓存是否有效
            if key in cache:
                result, timestamp = cache[key]
                if time.time() - timestamp < expire_seconds:
                    return result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache[key] = (result, time.time())
            return result
        
        return wrapper
    
    return decorator

@cache_result(expire_seconds=86400)  # 缓存一天
def get_trade_calendar(exchange='SSE', start_date=None, end_date=None, is_open=None):
    """
    获取交易日历数据
    
    Parameters:
    -----------
    exchange : str, 可选
        交易所代码,默认为'SSE'(上海证券交易所)
        可选值:SSE(上交所), SZSE(深交所), CFFEX(中金所), 
               SHFE(上期所), CZCE(郑商所), DCE(大商所), INE(上能源)
    start_date : str, 可选
        开始日期,格式为'YYYYMMDD',默认为当年第一天
    end_date : str, 可选
        结束日期,格式为'YYYYMMDD',默认为当年最后一天
    is_open : str, 可选
        是否交易,'0'表示休市,'1'表示交易,默认为None(全部)
        
    Returns:
    --------
    pandas.DataFrame
        包含交易日历信息的DataFrame
    """
    try:
        # 设置默认日期范围为当年
        if start_date is None or end_date is None:
            current_year = datetime.now().year
            if start_date is None:
                start_date = f"{current_year}0101"
            if end_date is None:
                end_date = f"{current_year}1231"
        
        # 获取Tushare API
        pro = get_pro_api()
        
        # 构建查询参数
        params = {
            'exchange': exchange,
            'start_date': start_date,
            'end_date': end_date
        }
        
        # 添加可选参数
        if is_open is not None:
            params['is_open'] = is_open
            
        # 查询交易日历
        df = pro.trade_cal(**params)
        log_progress(logger, f"成功获取交易日历数据: {len(df)}条记录")

        # 检查数据是否为空
        if df.empty:
            log_warning(logger, f"Tushare API返回空数据，可能原因：")
            log_warning(logger, f"1. Token无效或过期")
            log_warning(logger, f"2. API调用频率限制")
            log_warning(logger, f"3. 网络连接问题")
            log_warning(logger, f"请检查TUSHARE_TOKEN环境变量")

        return df

    except Exception as e:
        log_error(logger, f"获取交易日历数据失败: {str(e)}")
        log_error(logger, f"请检查：1. TUSHARE_TOKEN是否正确 2. 网络连接是否正常")
        return pd.DataFrame()  # 返回空DataFrame

def is_trade_day(date=None, exchange='SSE'):
    """
    判断给定日期是否为交易日
    
    Parameters:
    -----------
    date : datetime.date, datetime.datetime 或 str, 可选
        需要判断的日期,可以是datetime对象或'YYYY-MM-DD'格式的字符串
        如果为None,则默认为当天
    exchange : str, 可选
        交易所代码,默认为'SSE'(上海证券交易所)
        
    Returns:
    --------
    bool
        True表示是交易日,False表示非交易日
    """
    try:
        # 处理日期参数
        if date is None:
            date = datetime.now().date()
        elif isinstance(date, str):
            date = datetime.strptime(date, '%Y-%m-%d').date()
        elif isinstance(date, datetime):
            date = date.date()
            
        # 转换为Tushare格式的日期字符串
        date_str = date.strftime('%Y%m%d')
        
        # 获取交易日历
        calendar_df = get_trade_calendar(
            exchange=exchange,
            start_date=date_str,
            end_date=date_str
        )
        
        # 判断是否为交易日
        if calendar_df.empty:
            log_warning(logger, f"未找到日期 {date_str} 的交易日历数据")
            return False
            
        is_open = calendar_df.iloc[0]['is_open']
        return is_open == 1
        
    except Exception as e:
        log_error(logger, f"判断交易日失败: {str(e)}")
        return False

def get_latest_trade_day(date=None, exchange='SSE'):
    """
    获取给定日期当天或之前的最近一个交易日
    
    Parameters:
    -----------
    date : datetime.date, datetime.datetime 或 str, 可选
        参考日期,可以是datetime对象或'YYYY-MM-DD'格式的字符串
        如果为None,则默认为当天
    exchange : str, 可选
        交易所代码,默认为'SSE'(上海证券交易所)
        
    Returns:
    --------
    datetime.date
        最近的交易日日期
    """
    try:
        # 处理日期参数
        if date is None:
            date = datetime.now().date()
        elif isinstance(date, str):
            date = datetime.strptime(date, '%Y-%m-%d').date()
        elif isinstance(date, datetime):
            date = date.date()
            
        # 如果当天是交易日,直接返回
        if is_trade_day(date, exchange):
            return date
            
        # 获取当年的交易日历
        year = date.year
        start_date = f"{year}0101"
        end_date = f"{year}1231"
        
        calendar_df = get_trade_calendar(
            exchange=exchange,
            start_date=start_date,
            end_date=end_date,
            is_open='1'  # 只获取交易日
        )
        
        if calendar_df.empty:
            log_error(logger, f"未找到{year}年的交易日历数据")
            return date
            
        # 将日期转换为Tushare格式
        date_str = date.strftime('%Y%m%d')
        
        # 找到小于等于给定日期的最大交易日
        trade_days = calendar_df[calendar_df['cal_date'] <= date_str]['cal_date']
        
        if trade_days.empty:
            log_error(logger, f"未找到{date_str}之前的交易日")
            # 获取上一年的最后一个交易日
            prev_year = year - 1
            prev_end_date = f"{prev_year}1231"
            prev_start_date = f"{prev_year}0101"
            
            prev_calendar = get_trade_calendar(
                exchange=exchange,
                start_date=prev_start_date,
                end_date=prev_end_date,
                is_open='1'
            )
            
            if not prev_calendar.empty:
                latest_date_str = prev_calendar['cal_date'].max()
                return datetime.strptime(latest_date_str, '%Y%m%d').date()
            return date
            
        latest_date_str = trade_days.max()
        return datetime.strptime(latest_date_str, '%Y%m%d').date()
        
    except Exception as e:
        log_error(logger, f"获取最近交易日失败: {str(e)}")
        return date

def get_next_trade_day(date=None, exchange='SSE'):
    """
    获取给定日期之后的下一个交易日
    
    Parameters:
    -----------
    date : datetime.date, datetime.datetime 或 str, 可选
        参考日期,可以是datetime对象或'YYYY-MM-DD'格式的字符串
        如果为None,则默认为当天
    exchange : str, 可选
        交易所代码,默认为'SSE'(上海证券交易所)
        
    Returns:
    --------
    datetime.date
        下一个交易日日期
    """
    try:
        # 处理日期参数
        if date is None:
            date = datetime.now().date()
        elif isinstance(date, str):
            date = datetime.strptime(date, '%Y-%m-%d').date()
        elif isinstance(date, datetime):
            date = date.date()
            
        # 获取当年及下一年的交易日历(考虑年底情况)
        year = date.year
        start_date = f"{year}0101"
        end_date = f"{year+1}1231"
        
        calendar_df = get_trade_calendar(
            exchange=exchange,
            start_date=start_date,
            end_date=end_date,
            is_open='1'  # 只获取交易日
        )
        
        if calendar_df.empty:
            log_error(logger, f"未找到{year}-{year+1}年的交易日历数据")
            return date + timedelta(days=1)
            
        # 将日期转换为Tushare格式
        date_str = date.strftime('%Y%m%d')
        
        # 找到大于给定日期的最小交易日
        trade_days = calendar_df[calendar_df['cal_date'] > date_str]['cal_date']
        
        if trade_days.empty:
            log_error(logger, f"未找到{date_str}之后的交易日")
            return date + timedelta(days=1)
            
        next_date_str = trade_days.min()
        return datetime.strptime(next_date_str, '%Y%m%d').date()
        
    except Exception as e:
        log_error(logger, f"获取下一交易日失败: {str(e)}")
        return date + timedelta(days=1)

def get_friday_of_current_week(date):
    """
    获取指定日期所在周的周五日期
    如果当前日期是周六或周日，返回刚刚过去的周五
    
    参数:
    -----------
    date : datetime.date
        要查找对应周五的日期
        
    返回:
    --------
    datetime.date
        同一周的周五日期（如果是周六日则是刚过去的周五）
    """
    # 计算到周五的天数 (weekday 4 = 周五)
    weekday = date.weekday()
    if weekday < 5:  # 周一到周五
        days_to_friday = 4 - weekday
    else:  # 周六或周日，返回刚刚过去的周五
        days_to_friday = -(weekday - 4)
    
    friday = date + timedelta(days=days_to_friday)
    return friday

if __name__ == "__main__":
    # 测试交易日历功能
    today = datetime.now().date()
    
    # 测试是否为交易日
    is_today_trade = is_trade_day(today)
    log_progress(logger, f"今天 {today} {'是' if is_today_trade else '不是'}交易日")
    
    # 获取最近的交易日
    latest_trade_day = get_latest_trade_day(today)
    log_progress(logger, f"最近交易日是: {latest_trade_day}")
    
    # 获取下一个交易日
    next_trade_day = get_next_trade_day(today)
    log_progress(logger, f"下一个交易日是: {next_trade_day}")
    
    # 获取交易日历数据示例
    try:
        df = get_trade_calendar(start_date='20240101', end_date='20240131')
        if not df.empty and 'is_open' in df.columns:
            trade_days_count = len(df[df['is_open'] == 1])
            log_progress(logger, f"2024年1月共有 {trade_days_count} 个交易日")
        else:
            log_error(logger, "获取交易日历示例失败: 数据为空或缺少必要列")
    except Exception as e:
        log_error(logger, f"获取交易日历示例失败: {str(e)}")